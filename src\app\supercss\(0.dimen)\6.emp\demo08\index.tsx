import { DynamicViewportExample } from "./dynamic-viewport-example";
export default function Demo08() {
  return (
    <>
      <DynamicViewportExample unit="dvh" colorStyles="dark:bg-pink-500 bg-pink-500 border border-pink-400" />
      <DynamicViewportExample unit="lvh" colorStyles="dark:bg-blue-500 bg-blue-500 border border-blue-400" />
      <DynamicViewportExample unit="svh" colorStyles="dark:bg-purple-500 bg-purple-500 border border-purple-400" />
      <DynamicViewportExample unit="dvw" colorStyles="dark:bg-red-500 bg-red-500 border border-red-400" />
      <DynamicViewportExample unit="lvw" colorStyles="dark:bg-green-500 bg-green-500 border border-green-400" />
      <DynamicViewportExample unit="svw" colorStyles="dark:bg-yellow-500 bg-yellow-500 border border-yellow-400" />
    </>
  )
}